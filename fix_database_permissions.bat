@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

:: 防止闪退 - 在任何错误时都暂停
set "ERROR_OCCURRED=0"

:: ========================================
:: 数据库权限修复工具
:: 专门用于修复离线环境下的数据库权限问题
:: 版本: 2.0 - 增强离线环境兼容性
:: ========================================

title 数据库权限修复工具

echo.
echo ========================================
echo   数据库权限修复工具
echo ========================================
echo 功能: 修复SQLite数据库文件权限问题
echo 适用: 解决 "attempt to write a readonly database" 错误
echo ========================================
echo.

:: 获取脚本所在目录
set "PROJECT_ROOT=%~dp0"
set "PROJECT_ROOT=%PROJECT_ROOT:~0,-1%"

echo [调试] 原始路径: %~dp0
echo [调试] 处理后路径: %PROJECT_ROOT%

:: 检查目录是否存在
if not exist "%PROJECT_ROOT%" (
    echo [错误] 项目根目录不存在: %PROJECT_ROOT%
    set "ERROR_OCCURRED=1"
    goto :error_exit
)

cd /d "%PROJECT_ROOT%" 2>nul
if !errorlevel! neq 0 (
    echo [错误] 无法切换到项目目录: %PROJECT_ROOT%
    set "ERROR_OCCURRED=1"
    goto :error_exit
)

echo [信息] 项目根目录: %PROJECT_ROOT%
echo [信息] 当前工作目录: %CD%
echo.

:: ========================================
:: 第1步: 检查管理员权限
:: ========================================
echo [第1步/4] 检查管理员权限...

:: 检查是否以管理员身份运行
echo [调试] 正在检查管理员权限...
net session >nul 2>&1
set "admin_check_result=!errorlevel!"
echo [调试] 管理员权限检查结果: !admin_check_result!

if !admin_check_result! equ 0 (
    echo [成功] 已以管理员身份运行
    set "IS_ADMIN=1"
) else (
    echo [警告] 未以管理员身份运行，某些权限修复可能失败
    echo [建议] 右键点击此脚本，选择"以管理员身份运行"
    set "IS_ADMIN=0"
)
echo.

:: ========================================
:: 第2步: 查找数据库文件
:: ========================================
echo [第2步/4] 查找数据库文件...

set "db_files_found=0"
set "db_files="

:: 主数据库文件
if exist "%PROJECT_ROOT%\智能驾驶试验管控数据库.db" (
    set "db_files=!db_files! 智能驾驶试验管控数据库.db"
    set /a db_files_found+=1
    echo [找到] 主数据库: 智能驾驶试验管控数据库.db
)

:: 应用数据库文件
if exist "%PROJECT_ROOT%\app\tms_data.db" (
    set "db_files=!db_files! app\tms_data.db"
    set /a db_files_found+=1
    echo [找到] 应用数据库: app\tms_data.db
)

:: 测试数据库文件
if exist "%PROJECT_ROOT%\data\test_management.db" (
    set "db_files=!db_files! data\test_management.db"
    set /a db_files_found+=1
    echo [找到] 测试数据库: data\test_management.db
)

if !db_files_found! equ 0 (
    echo [警告] 未找到任何数据库文件
    echo [提示] 数据库文件将在首次运行应用程序时创建
) else (
    echo [信息] 共找到 !db_files_found! 个数据库文件
)
echo.

:: ========================================
:: 第3步: 修复文件权限
:: ========================================
echo [第3步/4] 修复文件权限...

for %%f in (!db_files!) do (
    echo [处理] 正在修复文件: %%f
    
    :: 移除只读属性
    attrib -R "%PROJECT_ROOT%\%%f" 2>nul
    if !errorlevel! equ 0 (
        echo   [成功] 已移除只读属性
    ) else (
        echo   [警告] 无法移除只读属性
    )
    
    :: 设置完全控制权限
    icacls "%PROJECT_ROOT%\%%f" /grant %USERNAME%:F 2>nul
    if !errorlevel! equ 0 (
        echo   [成功] 已设置完全控制权限
    ) else (
        echo   [警告] 无法设置完全控制权限
    )
    
    :: 处理WAL和SHM文件
    if exist "%PROJECT_ROOT%\%%f-wal" (
        attrib -R "%PROJECT_ROOT%\%%f-wal" 2>nul
        icacls "%PROJECT_ROOT%\%%f-wal" /grant %USERNAME%:F 2>nul
        echo   [信息] 已处理WAL文件权限
    )
    
    if exist "%PROJECT_ROOT%\%%f-shm" (
        attrib -R "%PROJECT_ROOT%\%%f-shm" 2>nul
        icacls "%PROJECT_ROOT%\%%f-shm" /grant %USERNAME%:F 2>nul
        echo   [信息] 已处理SHM文件权限
    )
    
    echo.
)

:: ========================================
:: 第4步: 修复目录权限
:: ========================================
echo [第4步/4] 修复目录权限...

:: 确保关键目录存在并具有正确权限
set "key_dirs=app data temp config"

for %%d in (!key_dirs!) do (
    if not exist "%PROJECT_ROOT%\%%d" (
        mkdir "%PROJECT_ROOT%\%%d" 2>nul
        echo [创建] 目录: %%d
    )
    
    icacls "%PROJECT_ROOT%\%%d" /grant %USERNAME%:F 2>nul
    if !errorlevel! equ 0 (
        echo [成功] 目录权限修复: %%d
    ) else (
        echo [警告] 目录权限修复失败: %%d
    )
)

echo.

:: ========================================
:: 权限验证测试
:: ========================================
echo [验证] 进行权限测试...

:: 查找Python解释器
set "PYTHON_CMD="
if exist "%PROJECT_ROOT%\python\python.exe" (
    set "PYTHON_CMD=%PROJECT_ROOT%\python\python.exe"
) else if exist "%PROJECT_ROOT%\.venv\Scripts\python.exe" (
    set "PYTHON_CMD=%PROJECT_ROOT%\.venv\Scripts\python.exe"
) else (
    where python >nul 2>&1
    if !errorlevel! equ 0 (
        set "PYTHON_CMD=python"
    )
)

if not "!PYTHON_CMD!"=="" (
    echo [测试] 使用Python进行数据库写入测试...
    
    for %%f in (!db_files!) do (
        echo [测试] 测试文件: %%f
        "!PYTHON_CMD!" -c "import sqlite3; conn = sqlite3.connect(r'%PROJECT_ROOT%\%%f'); conn.execute('CREATE TABLE IF NOT EXISTS test_permissions (id INTEGER)'); conn.execute('DROP TABLE IF EXISTS test_permissions'); conn.close(); print('  [成功] 数据库写入权限正常')" 2>nul
        if !errorlevel! neq 0 (
            echo   [失败] 数据库写入权限仍有问题
        )
    )
) else (
    echo [跳过] 未找到Python解释器，跳过数据库写入测试
)

echo.

:: ========================================
:: 完成报告
:: ========================================
echo ========================================
echo 权限修复完成
echo ========================================
echo.

if !db_files_found! gtr 0 (
    echo [结果] 已处理 !db_files_found! 个数据库文件
    echo [建议] 现在可以尝试重新运行 run.bat 启动应用程序
) else (
    echo [结果] 未找到现有数据库文件
    echo [说明] 数据库文件将在首次运行应用程序时自动创建
)

echo.
echo [注意事项]
echo 1. 如果问题仍然存在，请确保：
echo    - 项目目录不在受保护的系统目录中
echo    - 防病毒软件没有阻止数据库文件访问
echo    - 磁盘空间充足
echo.
echo 2. 如果是在网络驱动器上运行：
echo    - 建议将项目复制到本地磁盘
echo    - 确保网络驱动器支持文件锁定
echo.

echo.
echo [完成] 按任意键退出...
pause
exit /b 0

:: ========================================
:: 错误处理
:: ========================================
:error_exit
echo.
echo ========================================
echo [错误] 脚本执行失败
echo ========================================
echo.
echo [错误详情]
if "%ERROR_OCCURRED%"=="1" (
    echo - 发生了一个或多个错误，请查看上述错误信息
)
echo.
echo [建议解决方案]
echo 1. 确保以管理员身份运行此脚本
echo 2. 检查项目目录是否完整
echo 3. 确保磁盘空间充足
echo 4. 检查防病毒软件是否阻止操作
echo.
echo [手动修复命令]
echo 如果脚本无法自动修复，请在管理员命令提示符中手动执行：
echo.
echo   cd /d "%PROJECT_ROOT%"
echo   attrib -R "智能驾驶试验管控数据库.db"
echo   icacls "智能驾驶试验管控数据库.db" /grant %USERNAME%:F
echo   attrib -R "app\tms_data.db"
echo   icacls "app\tms_data.db" /grant %USERNAME%:F
echo.
echo ========================================
echo [错误] 按任意键退出...
pause
exit /b 1

@echo off
:: ========================================
:: 简单数据库权限修复工具
:: 专门解决 "attempt to write a readonly database" 错误
:: ========================================

echo.
echo ========================================
echo   简单数据库权限修复工具
echo ========================================
echo 功能: 修复SQLite数据库文件权限问题
echo 适用: 解决 "attempt to write a readonly database" 错误
echo ========================================
echo.

:: 暂停以便用户看到信息
timeout /t 2 >nul

echo [步骤1] 检查当前目录...
echo 当前目录: %CD%
echo.

echo [步骤2] 查找数据库文件...

:: 检查主数据库文件
if exist "智能驾驶试验管控数据库.db" (
    echo [找到] 主数据库: 智能驾驶试验管控数据库.db
    
    echo [修复] 移除只读属性...
    attrib -R "智能驾驶试验管控数据库.db"
    
    echo [修复] 设置文件权限...
    icacls "智能驾驶试验管控数据库.db" /grant %USERNAME%:F >nul 2>&1
    
    echo [完成] 主数据库权限修复完成
) else (
    echo [信息] 主数据库文件不存在，将在程序运行时创建
)

echo.

:: 检查app目录数据库
if exist "app\tms_data.db" (
    echo [找到] 应用数据库: app\tms_data.db
    
    echo [修复] 移除只读属性...
    attrib -R "app\tms_data.db"
    
    echo [修复] 设置文件权限...
    icacls "app\tms_data.db" /grant %USERNAME%:F >nul 2>&1
    
    echo [完成] 应用数据库权限修复完成
) else (
    echo [信息] 应用数据库文件不存在
)

echo.

:: 检查data目录数据库
if exist "data\test_management.db" (
    echo [找到] 测试数据库: data\test_management.db
    
    echo [修复] 移除只读属性...
    attrib -R "data\test_management.db"
    
    echo [修复] 设置文件权限...
    icacls "data\test_management.db" /grant %USERNAME%:F >nul 2>&1
    
    echo [完成] 测试数据库权限修复完成
) else (
    echo [信息] 测试数据库文件不存在
)

echo.

echo [步骤3] 确保目录权限...

:: 确保关键目录存在并有正确权限
if not exist "app" mkdir "app"
if not exist "data" mkdir "data"
if not exist "temp" mkdir "temp"

icacls "app" /grant %USERNAME%:F >nul 2>&1
icacls "data" /grant %USERNAME%:F >nul 2>&1
icacls "temp" /grant %USERNAME%:F >nul 2>&1

echo [完成] 目录权限设置完成

echo.

echo [步骤4] 权限修复验证...

:: 简单的权限测试
echo [测试] 创建测试文件...
echo test > "temp\permission_test.txt" 2>nul
if exist "temp\permission_test.txt" (
    del "temp\permission_test.txt" >nul 2>&1
    echo [成功] 文件写入权限正常
) else (
    echo [警告] 文件写入权限可能仍有问题
)

echo.
echo ========================================
echo 权限修复完成！
echo ========================================
echo.
echo [结果] 数据库文件权限已修复
echo [建议] 现在可以尝试运行 run.bat 启动程序
echo.
echo [注意] 如果问题仍然存在，请：
echo   1. 以管理员身份运行此脚本
echo   2. 检查防病毒软件是否阻止数据库访问
echo   3. 确保项目不在系统保护目录中
echo.

pause

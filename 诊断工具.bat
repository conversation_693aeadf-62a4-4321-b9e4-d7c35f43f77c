@echo off
:: ========================================
:: 系统诊断工具
:: 用于诊断权限修复脚本闪退的原因
:: ========================================

echo.
echo ========================================
echo   系统诊断工具
echo ========================================
echo 功能: 诊断系统环境和权限问题
echo ========================================
echo.

:: 基本信息收集
echo [诊断1] 基本系统信息
echo 操作系统: %OS%
echo 计算机名: %COMPUTERNAME%
echo 用户名: %USERNAME%
echo 当前目录: %CD%
echo 当前时间: %DATE% %TIME%
echo.

:: 检查管理员权限
echo [诊断2] 管理员权限检查
net session >nul 2>&1
if %errorlevel% equ 0 (
    echo [结果] 当前以管理员身份运行: 是
) else (
    echo [结果] 当前以管理员身份运行: 否
    echo [建议] 右键点击脚本选择"以管理员身份运行"
)
echo.

:: 检查项目文件结构
echo [诊断3] 项目文件结构检查
echo [检查] run.bat 文件...
if exist "run.bat" (
    echo [存在] run.bat
) else (
    echo [缺失] run.bat
)

echo [检查] 数据库文件...
if exist "智能驾驶试验管控数据库.db" (
    echo [存在] 智能驾驶试验管控数据库.db
    
    :: 检查文件属性
    for %%f in ("智能驾驶试验管控数据库.db") do (
        echo [属性] 文件大小: %%~zf 字节
        echo [属性] 修改时间: %%~tf
    )
    
    :: 检查只读属性
    attrib "智能驾驶试验管控数据库.db" | find "R" >nul
    if %errorlevel% equ 0 (
        echo [属性] 只读属性: 是 (这可能是问题原因)
    ) else (
        echo [属性] 只读属性: 否
    )
) else (
    echo [缺失] 智能驾驶试验管控数据库.db (程序运行时会创建)
)

echo [检查] 关键目录...
if exist "app" (
    echo [存在] app 目录
) else (
    echo [缺失] app 目录
)

if exist "src" (
    echo [存在] src 目录
) else (
    echo [缺失] src 目录
)

if exist "python" (
    echo [存在] python 目录 (内置Python)
) else (
    echo [缺失] python 目录
)

if exist ".venv" (
    echo [存在] .venv 目录 (虚拟环境)
) else (
    echo [缺失] .venv 目录
)
echo.

:: 检查Python环境
echo [诊断4] Python环境检查
if exist "python\python.exe" (
    echo [找到] 内置Python解释器
    "python\python.exe" --version 2>nul
    if %errorlevel% equ 0 (
        echo [状态] 内置Python可正常运行
    ) else (
        echo [状态] 内置Python无法运行
    )
) else (
    echo [未找到] 内置Python解释器
)

if exist ".venv\Scripts\python.exe" (
    echo [找到] 虚拟环境Python解释器
    ".venv\Scripts\python.exe" --version 2>nul
    if %errorlevel% equ 0 (
        echo [状态] 虚拟环境Python可正常运行
    ) else (
        echo [状态] 虚拟环境Python无法运行
    )
) else (
    echo [未找到] 虚拟环境Python解释器
)

where python >nul 2>&1
if %errorlevel% equ 0 (
    echo [找到] 系统Python解释器
    python --version 2>nul
    if %errorlevel% equ 0 (
        echo [状态] 系统Python可正常运行
    ) else (
        echo [状态] 系统Python无法运行
    )
) else (
    echo [未找到] 系统Python解释器
)
echo.

:: 检查磁盘空间
echo [诊断5] 磁盘空间检查
for /f "tokens=3" %%a in ('dir /-c ^| find "bytes free"') do set free_space=%%a
echo [信息] 当前驱动器可用空间: %free_space% 字节
echo.

:: 检查环境变量
echo [诊断6] 关键环境变量
echo PATH前100字符: %PATH:~0,100%...
echo TEMP目录: %TEMP%
echo TMP目录: %TMP%
echo.

echo ========================================
echo 诊断完成
echo ========================================
echo.
echo [总结] 请检查上述诊断结果中的问题项
echo [建议] 如果发现只读属性问题，请运行"简单权限修复.bat"
echo.

pause
